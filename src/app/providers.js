// app/providers.js
'use client'

import { HeroUIProvider } from '@heroui/react'
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ToastProvider } from './components/ui/Toast';

export function Providers({ children }) {
  return (
    <HeroUIProvider>
      <NextThemesProvider attribute="class" defaultTheme="light">
        <ToastProvider>
          {children}
        </ToastProvider>
      </NextThemesProvider>
    </HeroUIProvider>
  )
}
