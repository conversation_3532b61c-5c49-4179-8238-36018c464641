'use client';

import { createContext, useContext, useState, useCallback } from 'react';
import { createRoot } from 'react-dom/client';
import { Card, CardBody, Button } from '@heroui/react';
import { RiCloseLine, RiCheckLine, RiErrorWarningLine, RiInformationLine } from '@remixicon/react';

// Toast Context
const ToastContext = createContext();

// Toast Provider Component
export function ToastProvider({ children }) {
  const [toasts, setToasts] = useState([]);

  const addToast = useCallback((toast) => {
    const id = Date.now() + Math.random();
    const newToast = {
      id,
      type: 'info',
      duration: 4000,
      ...toast,
    };

    setToasts(prev => [...prev, newToast]);

    // Auto remove toast after duration
    if (newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }

    return id;
  }, []);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const value = {
    addToast,
    removeToast,
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </ToastContext.Provider>
  );
}

// Toast Container Component
function ToastContainer({ toasts, onRemove }) {
  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(toast => (
        <ToastItem key={toast.id} toast={toast} onRemove={onRemove} />
      ))}
    </div>
  );
}

// Individual Toast Item Component
function ToastItem({ toast, onRemove }) {
  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <RiCheckLine className="w-5 h-5 text-green-500" />;
      case 'error':
        return <RiErrorWarningLine className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <RiErrorWarningLine className="w-5 h-5 text-yellow-500" />;
      default:
        return <RiInformationLine className="w-5 h-5 text-blue-500" />;
    }
  };

  const getColorClasses = () => {
    switch (toast.type) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <Card className={`min-w-[300px] max-w-[400px] ${getColorClasses()} border animate-in slide-in-from-right duration-300`}>
      <CardBody className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="flex-1 min-w-0">
            {toast.title && (
              <p className="font-semibold text-sm text-gray-900 mb-1">
                {toast.title}
              </p>
            )}
            {toast.message && (
              <p className="text-sm text-gray-700">
                {toast.message}
              </p>
            )}
          </div>
          <Button
            isIconOnly
            size="sm"
            variant="light"
            onPress={() => onRemove(toast.id)}
            className="flex-shrink-0"
          >
            <RiCloseLine className="w-4 h-4" />
          </Button>
        </div>
      </CardBody>
    </Card>
  );
}

// Hook to use toast
export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

// Standalone toast function for use outside React components
class ToastManager {
  static container = null;
  static root = null;

  static createContainer() {
    if (typeof window === 'undefined') return;
    
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'toast-container';
      document.body.appendChild(this.container);
      this.root = createRoot(this.container);
    }
  }

  static show(toast) {
    if (typeof window === 'undefined') return;
    
    this.createContainer();
    
    const StandaloneToast = () => {
      const [visible, setVisible] = useState(true);
      
      const handleClose = () => {
        setVisible(false);
        setTimeout(() => {
          this.root?.unmount();
          if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
            this.container = null;
            this.root = null;
          }
        }, 300);
      };

      // Auto close after duration
      useState(() => {
        if (toast.duration !== 0) {
          const timer = setTimeout(handleClose, toast.duration || 4000);
          return () => clearTimeout(timer);
        }
      }, []);

      if (!visible) return null;

      return (
        <div className="fixed top-4 right-4 z-50">
          <ToastItem toast={{ id: 1, type: 'info', ...toast }} onRemove={handleClose} />
        </div>
      );
    };

    this.root?.render(<StandaloneToast />);
  }
}

// Export standalone toast function
export const toast = {
  success: (message, options = {}) => ToastManager.show({ type: 'success', message, ...options }),
  error: (message, options = {}) => ToastManager.show({ type: 'error', message, ...options }),
  warning: (message, options = {}) => ToastManager.show({ type: 'warning', message, ...options }),
  info: (message, options = {}) => ToastManager.show({ type: 'info', message, ...options }),
  show: (toast) => ToastManager.show(toast),
};
