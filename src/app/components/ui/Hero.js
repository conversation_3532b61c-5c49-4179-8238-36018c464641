'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardBody, CardFooter, Input, Button } from '@heroui/react';
import { useState } from 'react';

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [numberOfPins, setNumberOfPins] = useState(288);
  const [maxLines, setMaxLines] = useState(4000);
  const [lineHeight, setLineHeight] = useState(20);

  return (
    <div className="text-center pt-16 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('String Art Generator')}
      </h1>
      <Card className="mt-10 max-w-[678px] mx-auto">
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input label={t("Number of Pins")}
              defaultValue={288}
              value={numberOfPins}
              onChange={(e) => setNumberOfPins(e.target.value)}
            ></Input>
            <Input label={t("Number of Lines")}
              defaultValue={4000}
              value={maxLines}
              onChange={(e) => setMaxLines(e.target.value)}
            ></Input>
            <Input label={t("Line Weight")}
              defaultValue={20}
              value={lineHeight}
              onChange={(e) => setLineHeight(e.target.value)}
            ></Input>
          </div>
          <div className="mt-6 flex justify-center">
            <Button color="primary" className="bg-blue-500 hover:bg-blue-600">
              {t("Upload Image")}
            </Button>
          </div>
        </CardBody>
      </Card>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-10'>
        <Card className="">
          <CardBody>
            <canvas id="inputCanvas" width="500" height="500"></canvas>
          </CardBody>
          <CardFooter>
            <Button>{t("Generate")}</Button>
          </CardFooter>
        </Card>
        <Card className="">
          <CardBody>
            <canvas id="outputCanvas" width="500" height="500"></canvas>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}
