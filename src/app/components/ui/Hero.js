'use client';
import { getTranslation } from '@/lib/i18n';
import { <PERSON>, CardBody, CardFooter, Input, Button, addToast } from '@heroui/react';
import { useState, useRef } from 'react';
import OriginalStringArtAlgorithm from '@/lib/originalAlgorithm';

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [numberOfPins, setNumberOfPins] = useState(288);
  const [maxLines, setMaxLines] = useState(4000);
  const [lineWeight, setLineWeight] = useState(20);
  const [uploadedImage, setUploadedImage] = useState(null);
  const fileInputRef = useRef(null);
  const [currentImage, setCurrentImage] = useState(null);

  const inputCanvasRef = useRef(null);
  const outputCanvasRef = useRef(null);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setUploadedImage(img);
          drawImageToCanvas(img);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const drawImageToCanvas = (img) => {
    const canvas = inputCanvasRef.current;
    if (canvas) {
      const inputCtx = canvas.getContext('2d');
      const size = Math.min(canvas.width, canvas.height);

      // Clear the canvas
      inputCtx.clearRect(0, 0, canvas.width, canvas.height);

      // 计算居中裁剪
      const scale = Math.min(img.width, img.height);
      const x = (img.width - scale) / 2;
      const y = (img.height - scale) / 2;

      inputCtx.drawImage(img, x, y, scale, scale, 0, 0, size, size);

      // 应用圆形遮罩
      inputCtx.globalCompositeOperation = 'destination-in';
      inputCtx.beginPath();
      inputCtx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
      inputCtx.closePath();
      inputCtx.fill();
      inputCtx.globalCompositeOperation = 'source-over';

      setCurrentImage(inputCtx.getImageData(0, 0, size, size));
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleGenerate = async () => {
    if (!currentImage) {
      addToast({
        title: t('Please upload an image first'),
        color: 'Warning'
      });
      return;
    }

    const startTime = Date.now();

    const outputCtx = outputCanvasRef.current.getContext('2d');

    try {
      // 创建算法实例
      algorithm = new OriginalStringArtAlgorithm({
        imageSize: 500,
        nPins: numberOfPins,
        maxLines: maxLines,
        lineWeight: lineWeight,
        minDistance: 20,
        hoopDiameter: 0.625,
        scale: 20
      });

      // 生成字符串艺术
      const result = await algorithm.generate(currentImage, (progress) => {
        // status.textContent = getStatusText(progress);
        // progressBar.style.width = (progress.progress * 100) + '%';

        // 如果是绘制线条阶段，实时更新输出画布
        if (progress.step === 'drawing-lines' && progress.linesDrawn % 100 === 0) {
          drawCurrentProgress(algorithm, outputCtx);
        }
      });

      // 绘制最终结果
      drawResult(outputCtx, result);

      // // 显示结果信息
      // const endTime = Date.now();
      // const processingTime = (endTime - startTime) / 1000;

      // document.getElementById('lineCount').textContent = result.lineSequence.length;
      // document.getElementById('threadLength').textContent = result.threadLength.toFixed(2);
      // document.getElementById('processingTime').textContent = processingTime.toFixed(1);
      // document.getElementById('pinSequence').value = result.lineSequence.join(',');

      // results.classList.remove('hidden');
      // status.textContent = '生成完成！';

    } catch (error) {
      console.error('生成失败:', error);
      // status.textContent = '生成失败: ' + error.message;
    } finally {
      // generateBtn.disabled = false;
      // progressBar.style.width = '0%';
    }
  }

  const drawCurrentProgress = (algorithm, outputCtx) => {
    if (!algorithm || !algorithm.line_sequence || !algorithm.pin_coords) return;

    const size = 500;
    // 与原版本一致：画布大小为 IMG_SIZE * 2
    outputCanvasRef.current.width = size * 2;
    outputCanvasRef.current.height = size * 2;

    // 清空画布（白色背景）
    outputCtx.fillStyle = 'white';
    outputCtx.fillRect(0, 0, size * 2, size * 2);

    // 与原版本完全一致的绘制参数
    outputCtx.strokeStyle = 'black';
    outputCtx.lineWidth = 0.3;  // 与原版本一致
    outputCtx.globalAlpha = 1.0;  // 与原版本一致，不使用透明度

    for (let i = 0; i < algorithm.line_sequence.length - 1; i++) {
      const pin1 = algorithm.pin_coords[algorithm.line_sequence[i]];
      const pin2 = algorithm.pin_coords[algorithm.line_sequence[i + 1]];

      outputCtx.beginPath();
      // 与原版本一致：坐标乘以2
      outputCtx.moveTo(pin1[0] * 2, pin1[1] * 2);
      outputCtx.lineTo(pin2[0] * 2, pin2[1] * 2);
      outputCtx.stroke();
    }
  }

  const drawResult = (outputCtx, result) => {
    if (!result || !result.lineSequence || !result.pinCoords) return;

    const size = 500;
    // 与原版本一致：画布大小为 IMG_SIZE * 2
    outputCanvasRef.current.width = size * 2;
    outputCanvasRef.current.height = size * 2;

    // 清空画布（白色背景）
    outputCtx.fillStyle = 'white';
    outputCtx.fillRect(0, 0, size * 2, size * 2);

    // 与原版本完全一致的绘制参数
    outputCtx.strokeStyle = 'black';
    outputCtx.lineWidth = 0.3;  // 与原版本一致
    outputCtx.globalAlpha = 1.0;  // 与原版本一致，不使用透明度

    for (let i = 0; i < result.lineSequence.length - 1; i++) {
      const pin1 = result.pinCoords[result.lineSequence[i]];
      const pin2 = result.pinCoords[result.lineSequence[i + 1]];

      outputCtx.beginPath();
      // 与原版本一致：坐标乘以2
      outputCtx.moveTo(pin1[0] * 2, pin1[1] * 2);
      outputCtx.lineTo(pin2[0] * 2, pin2[1] * 2);
      outputCtx.stroke();
    }
  }

  return (
    <div className="text-center pt-16 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('String Art Generator')}
      </h1>
      <Card className="mt-10 max-w-[678px] mx-auto">
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input label={t("Number of Pins")}
              defaultValue={288}
              value={numberOfPins}
              onChange={(e) => setNumberOfPins(e.target.value)}
            ></Input>
            <Input label={t("Number of Lines")}
              defaultValue={4000}
              value={maxLines}
              onChange={(e) => setMaxLines(e.target.value)}
            ></Input>
            <Input label={t("Line Weight")}
              defaultValue={20}
              value={lineWeight}
              onChange={(e) => setLineWeight(e.target.value)}
            ></Input>
          </div>
          <div className="mt-6 flex justify-center">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageUpload}
              accept="image/*"
              className="hidden"
            />
            <Button
              color="primary"
              className="bg-blue-500 hover:bg-blue-600 w-full"
              onClick={handleUploadClick}
            >
              {t("Upload Image")}
            </Button>
          </div>
        </CardBody>
      </Card>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-10'>
        <Card>
          <CardBody>
            <canvas ref={inputCanvasRef} width="500" height="500"></canvas>
          </CardBody>
          <CardFooter>
            <Button className="w-full" color="primary" onPress={handleGenerate}>{t("Generate String Art")}</Button>
          </CardFooter>
        </Card>
        <Card>
          <CardBody>
            <canvas ref={outputCanvasRef} width="500" height="500"></canvas>
          </CardBody>
          <CardFooter>

          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
