'use client';
import { getTranslation } from '@/lib/i18n';
import { Card, CardBody, CardFooter, Input, Button } from '@heroui/react';
import { useState, useRef } from 'react';

export default function Hero({ locale = 'en' }) {
  const t = function (key) {
    return getTranslation(locale, key);
  }

  const [numberOfPins, setNumberOfPins] = useState(288);
  const [maxLines, setMaxLines] = useState(4000);
  const [lineHeight, setLineHeight] = useState(20);
  const [uploadedImage, setUploadedImage] = useState(null);
  const fileInputRef = useRef(null);

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          setUploadedImage(img);
          drawImageToCanvas(img);
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };

  const drawImageToCanvas = (img) => {
    const canvas = document.getElementById('inputCanvas');
    if (canvas) {
      const ctx = canvas.getContext('2d');
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;

      // Clear the canvas
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // Calculate scaling to fit image while maintaining aspect ratio
      const imgAspectRatio = img.width / img.height;
      const canvasAspectRatio = canvasWidth / canvasHeight;

      let drawWidth, drawHeight, offsetX, offsetY;

      if (imgAspectRatio > canvasAspectRatio) {
        // Image is wider than canvas ratio
        drawWidth = canvasWidth;
        drawHeight = canvasWidth / imgAspectRatio;
        offsetX = 0;
        offsetY = (canvasHeight - drawHeight) / 2;
      } else {
        // Image is taller than canvas ratio
        drawWidth = canvasHeight * imgAspectRatio;
        drawHeight = canvasHeight;
        offsetX = (canvasWidth - drawWidth) / 2;
        offsetY = 0;
      }

      // Draw the image
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
    }
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="text-center pt-16 pb-2">
      <h1 className="text-5xl font-bold text-primary mb-2">
        {t('String Art Generator')}
      </h1>
      <Card className="mt-10 max-w-[678px] mx-auto">
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input label={t("Number of Pins")}
              defaultValue={288}
              value={numberOfPins}
              onChange={(e) => setNumberOfPins(e.target.value)}
            ></Input>
            <Input label={t("Number of Lines")}
              defaultValue={4000}
              value={maxLines}
              onChange={(e) => setMaxLines(e.target.value)}
            ></Input>
            <Input label={t("Line Weight")}
              defaultValue={20}
              value={lineHeight}
              onChange={(e) => setLineHeight(e.target.value)}
            ></Input>
          </div>
          <div className="mt-6 flex justify-center">
            <Button color="primary" className="bg-blue-500 hover:bg-blue-600">
              {t("Upload Image")}
            </Button>
          </div>
        </CardBody>
      </Card>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mt-10'>
        <Card className="">
          <CardBody>
            <canvas id="inputCanvas" width="500" height="500"></canvas>
          </CardBody>
          <CardFooter>
            <Button className="w-full" color="primary">{t("Generate String Art")}</Button>
          </CardFooter>
        </Card>
        <Card className="">
          <CardBody>
            <canvas id="outputCanvas" width="500" height="500"></canvas>
          </CardBody>
          <CardFooter>

          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
